import { FollowUpBadge, type FollowUpStatusType, LEAD_STATUS_OPTIONS } from "@components/badge";
import { Avatar, Input, Select, Textarea } from "@components/common";
import type { FormField, FormValues } from "@components/leadDrawer/interface";
import { TagChevronIcon } from "@phosphor-icons/react";
import { useForm } from "@tanstack/react-form";
import { cn } from "@utils/cn";
import { useTranslation } from "react-i18next";
import { leadProfileField } from "./prolfileFields";

export const LeadProfileForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const noteField: FormField = {
    className: "w-full flex-1 resize-none",
    id: "note",
    label: t("addLead.note"),
    placeholder: t("addLead.note"),
    type: "textarea",
  };

  const renderField = (field: FormField) => {
    const validators = field.required
      ? {
          onChange: ({ value }: { value: string }) =>
            !value ? `${field.label} is required` : undefined,
        }
      : {};

    return (
      <form.Field key={field.id} name={field.id as keyof FormValues} validators={validators}>
        {(fieldApi) => {
          const isErrors = fieldApi.state.meta.errors.length > 0;
          return (
            <div className="grid grid-cols-2">
              <label htmlFor={fieldApi.name} className="flex gap-1 self-center text-h6">
                <span className="text-h6">{field.label}</span>
                {field.required && <span className="text-error text-h6">*</span>}
              </label>

              <div className="relative">
                {field.type === "input" && (
                  <Input
                    id={fieldApi.name}
                    type={field.inputType || "text"}
                    placeholder={field.placeholder}
                    value={fieldApi.state.value}
                    onChange={(e) => fieldApi.handleChange(e.target.value)}
                    disabled={field.disabled}
                    className={cn(
                      "flex-1",
                      field.variant === "transparent" ? "group-hover:bg-base-200 " : "",
                      isErrors ? "outline-1 outline-error" : "",
                    )}
                    variant={field.variant || "default"}
                  />
                )}

                {field.type === "date" && (
                  <Input
                    id={fieldApi.name}
                    type="date"
                    value={fieldApi.state.value}
                    onChange={(e) => fieldApi.handleChange(e.target.value)}
                    disabled={field.disabled}
                    className="flex-1"
                  />
                )}

                {field.type === "select" && (
                  <Select
                    id={fieldApi.name}
                    options={field.options}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={cn("flex-1", isErrors ? "outline-1 outline-error" : "")}
                    placeholder={field.placeholder}
                  />
                )}

                {isErrors && (
                  <div className="-bottom-5 absolute left-1 text-error text-xs">
                    {fieldApi.state.meta.errors[0]}
                  </div>
                )}
              </div>
            </div>
          );
        }}
      </form.Field>
    );
  };

  const renderNoteField = (field: FormField) => {
    return (
      <form.Field key={field.id} name={field.id as keyof FormValues}>
        {(fieldApi) => (
          <div className="flex min-h-0 flex-1 flex-col gap-2">
            <label htmlFor={fieldApi.name} className="text-h6">
              {field.label}
            </label>
            <Textarea
              className={field.className}
              id={fieldApi.name}
              placeholder={field.placeholder}
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
            />
            {fieldApi.state.meta.errors.length > 0 && (
              <div className="text-error text-sm">{fieldApi.state.meta.errors[0]}</div>
            )}
          </div>
        )}
      </form.Field>
    );
  };

  const renderLeadDetails = () => {
    return (
      <>
        <form.Field name="leadStatus">
          {(fieldApi) => (
            <div className="flex flex-col items-center gap-1">
              <Avatar
                className="w-10"
                image={"https://img.daisyui.com/images/profile/demo/<EMAIL>"}
              />
              <Select
                id={fieldApi.name}
                options={LEAD_STATUS_OPTIONS}
                size="sm"
                variant="popup"
                value={fieldApi.state.value || "active"}
                onChange={(value) => fieldApi.handleChange(value)}
                className={cn("flex-1")}
                placeholder=""
              />
            </div>
          )}
        </form.Field>
        <div className="flex flex-col gap-2">
          <form.Field name="name">
            {(fieldApi) => (
              <Input
                id={fieldApi.name}
                type="text"
                placeholder="Name"
                value={fieldApi.state.value}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
                className={cn(
                  "flex-1 group-hover:bg-base-200",
                  // field.variant === "transparent" ? "group-hover:bg-base-200 " : "",
                  // isErrors ? "outline-1 outline-error" : "",
                )}
                variant="transparent"
              />
            )}
          </form.Field>
          <div className="flex gap-2">
            <TagChevronIcon size={20} className="text-info" />
            <form.Field name="followUpStatus">
              {(fieldApi) => (
                <FollowUpBadge type={(fieldApi.state.value as FollowUpStatusType) || "pending"} />
              )}
            </form.Field>
          </div>
        </div>
      </>
    );
  };

  const form = useForm({
    defaultValues: {
      contactChannel: "",
      contactInfo: "",
      followUpDate: "",
      followUpStatus: "",
      lastFollowUpDate: "",
      leadStatus: "",
      name: "",
      note: "",
      opportunity: "",
      servicesOfInterest: "",
      startDate: today,
      totalDayFromStartDate: "",
      totalDayToNextFollowUp: "",
    },
    onSubmit: async ({ value }: { value: FormValues }) => {
      alert(value);
    },
  });

  return (
    <form
      className="flex h-full flex-col gap-6 overflow-hidden p-1"
      onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="flex gap-6">{renderLeadDetails()}</div>
      <div className="grid grid-cols-1 gap-y-2">
        {leadProfileField(t).map((field) => renderField(field))}
      </div>
      {renderNoteField(noteField)}

      {/* Buttons */}
      {/* <div className="flex justify-between">
        <Button
          variant="outline"
          className="w-28 hover:border-primary-content/30 hover:bg-primary-content/30"
          type="button"
          // onClick={handleDrawerClose}
        >
          {t("common.cancel")}
        </Button>

        <div className="flex gap-3">
          <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
            {([canSubmit, isSubmitting]) => (
              <Button
                variant="outline"
                className={cn(
                  "w-28 border-primary text-primary",
                  "hover:border-primary-content hover:bg-primary-content/10 hover:text-primary-content",
                  { "border-none text-base-100": !canSubmit },
                )}
                type="submit"
                disabled={!canSubmit || isSubmitting}
              >
                {isSubmitting ? t("common.loading") : t("common.draft")}
              </Button>
            )}
          </form.Subscribe>

          <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
            {([canSubmit, isSubmitting]) => (
              <Button className="w-28" type="submit" disabled={!canSubmit || isSubmitting}>
                {isSubmitting ? t("common.loading") : t("common.save")}
              </Button>
            )}
          </form.Subscribe>
        </div>
      </div> */}
    </form>
  );
};
